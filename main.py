import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from typing import List, Dict, Tuple, Optional, Union
from datetime import timedelta
import warnings
from pathlib import Path
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import math
from matplotlib.gridspec import GridSpec

# ============================================================================
# КОНФИГУРАЦИЯ И НАСТРОЙКИ
# ============================================================================
class ReportConfig:
    """Централизованная конфигурация системы отчетности"""
    
    def __init__(self):
        self.THRESHOLD_DELAYED_MINUTES: int = 15
        self.OUTLIER_Z_SCORE: float = 2.5
        self.MIN_SAMPLES_FOR_OUTLIERS: int = 5
        
        self.METRICS: Dict[str, Tuple[str, Tuple[float, float], str]] = {
            'order2trip': ('Базовая конверсия Order→Trip', (0, 100), 'Общая эффективность системы'),
            'order2offer': ('Конверсия Order→Offer', (0, 100), 'Скорость формирования предложений'),
            'offer2assign': ('Конверсия Offer→Assign', (0, 100), 'Качество матчинга водителей'),
            'assign2arrive': ('Конверсия Assign→Arrive', (0, 100), 'Надежность водителей'),
            'arrive2trip': ('Конверсия Arrive→Trip', (0, 100), 'Финальная конверсия')
        }
        
        self.FIGURE_SIZE: Tuple[int, int] = (16, 10)
        self.DPI: int = 150
        
        self.COLORS_PALETTE = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D',
            '#6A994E', '#577590', '#F77F00', '#D62828',
            '#003049', '#669BBC', '#80ED99', '#F8F8FF'
        ]
        
        self.LINE_STYLES = ['-', '--', '-.', ':']
        self.MARKERS = ['o', 's', '^', 'v', 'D', 'P', '*', 'X']
        
        self.GRID_ALPHA: float = 0.2
        self.ANNOTATION_FONTSIZE: int = 9
        self.LEGEND_FONTSIZE: int = 10
        self.TITLE_FONTSIZE: int = 14
        self.LABEL_FONTSIZE: int = 11

# ============================================================================
# УТИЛИТЫ ДЛЯ ОБРАБОТКИ ДАННЫХ
# ============================================================================
class DataProcessor:
    """Упрощенный класс для обработки данных"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """Базовая валидация"""
        if df.empty:
            raise ValueError("DataFrame пуст")
        
        df = df.drop_duplicates(subset=['id_order'])
        return df
    
    @staticmethod
    def parse_datetime_columns(df: pd.DataFrame) -> pd.DataFrame:
        """Упрощенный парсинг дат"""
        df = df.copy()
        datetime_cols = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']
        
        for col in datetime_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        return df
    
    @staticmethod
    def detect_delayed_orders(df: pd.DataFrame, threshold_minutes: int = 15) -> pd.DataFrame:
        """Упрощенная детекция отложенных заказов"""
        df = df.copy()
        time_diff = (df['offer_time'] - df['order_time']).dt.total_seconds() / 60
        df['is_delayed'] = (time_diff > threshold_minutes).astype(int)
        df['delay_minutes'] = time_diff.fillna(0)
        return df

class MetricsCalculator:
    """Упрощенный расчет метрик"""
    
    @staticmethod
    def calculate_conversion_metrics(df: pd.DataFrame, group_cols: List[str]) -> pd.DataFrame:
        """Расчет метрик конверсии с исправлением конфликта колонок"""
        
        df_work = df.copy()
        
        agg_dict = {
            'id_order': 'count',
            'offer_time': lambda x: x.notna().sum(),
            'assign_time': lambda x: x.notna().sum(),
            'arrive_time': lambda x: x.notna().sum(),
            'trip_time': lambda x: x.notna().sum(),
            'is_delayed': 'sum',
            'delay_minutes': 'mean'
        }
        
        result = df_work.groupby(group_cols).agg(agg_dict)
        result.columns = ['cnt_order', 'cnt_offer', 'cnt_assign', 'cnt_arrive', 'cnt_trip', 'cnt_delayed', 'avg_delay_minutes']
        result = result.reset_index()
        
        result['order2trip'] = np.where(result['cnt_order'] > 0, result['cnt_trip'] / result['cnt_order'], 0)
        result['order2offer'] = np.where(result['cnt_order'] > 0, result['cnt_offer'] / result['cnt_order'], 0)
        result['offer2assign'] = np.where(result['cnt_offer'] > 0, result['cnt_assign'] / result['cnt_offer'], 0)
        result['assign2arrive'] = np.where(result['cnt_assign'] > 0, result['cnt_arrive'] / result['cnt_assign'], 0)
        result['arrive2trip'] = np.where(result['cnt_arrive'] > 0, result['cnt_trip'] / result['cnt_arrive'], 0)
        result['delayed_ratio'] = np.where(result['cnt_order'] > 0, result['cnt_delayed'] / result['cnt_order'], 0)
        
        return result

# ============================================================================
# УЛУЧШЕННАЯ СИСТЕМА ВИЗУАЛИЗАЦИИ
# ============================================================================
class ModernVisualizer:
    """Современная система визуализации с улучшенным дизайном"""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self.setup_style()
    
    def setup_style(self):
        """Настройка современного стиля"""
        plt.style.use('default')
        
        custom_style = {
            'figure.figsize': self.config.FIGURE_SIZE,
            'figure.dpi': self.config.DPI,
            'figure.facecolor': 'white',
            'axes.facecolor': '#f8f9fa',
            'axes.edgecolor': '#dee2e6',
            'axes.linewidth': 1,
            'axes.grid': True,
            'axes.axisbelow': True,
            'grid.color': '#e9ecef',
            'grid.alpha': self.config.GRID_ALPHA,
            'axes.titlesize': self.config.TITLE_FONTSIZE,
            'axes.labelsize': self.config.LABEL_FONTSIZE,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': self.config.LEGEND_FONTSIZE,
            'legend.frameon': True,
            'legend.fancybox': True,
            'legend.shadow': False,
            'legend.framealpha': 0.9,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.spines.left': True,
            'axes.spines.bottom': True,
        }
        
        plt.rcParams.update(custom_style)
    
    def create_city_separate_plots(self, df: pd.DataFrame, metric: str, 
                                  title: str, description: str, ylim: Tuple[float, float]):
        """Создание отдельных графиков для каждого города"""
        
        cities = sorted(df['city'].unique())
        n_cities = len(cities)
        
        if n_cities <= 2:
            fig, axes = plt.subplots(1, n_cities, figsize=(8 * n_cities, 6))
        elif n_cities <= 4:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        else:
            n_cols = min(3, n_cities)
            n_rows = math.ceil(n_cities / n_cols)
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(6 * n_cols, 5 * n_rows))
        
        if n_cities == 1:
            axes = [axes]
        elif n_cities > 1:
            axes = axes.flatten() if hasattr(axes, 'flatten') else [axes]
        
        fig.suptitle(f"{title}\n{description}", fontsize=16, fontweight='bold', y=0.98)
        
        for i, city in enumerate(cities):
            ax = axes[i]
            color = self.config.COLORS_PALETTE[i % len(self.config.COLORS_PALETTE)]
            
            city_data = df[df['city'] == city].sort_values('day_order')
            
            if not city_data.empty:
                ax.plot(city_data['day_order'], city_data[metric] * 100,
                       color=color, linewidth=3, marker='o', markersize=8,
                       markerfacecolor=color, markeredgecolor='white', markeredgewidth=2)
                
                mean_value = city_data[metric].mean() * 100
                ax.axhline(y=mean_value, color=color, linestyle='--', alpha=0.7,
                          label=f'Среднее: {mean_value:.1f}%')
                
                total_orders = city_data['cnt_order'].sum()
                ax.set_title(f"{city}\n({total_orders:,} заказов)", fontweight='bold', pad=15)
                ax.set_ylabel("Конверсия (%)", fontweight='bold')
                ax.set_xlabel("День месяца", fontweight='bold')
                ax.set_ylim(ylim)
                
                ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0f}%'))
                
                weekend_days = [6, 7, 13, 14, 20, 21, 27, 28]
                for day in weekend_days:
                    if day in city_data['day_order'].values:
                        ax.axvspan(day-0.5, day+0.5, alpha=0.1, color='red', zorder=0)
                
                ax.legend(loc='upper right', framealpha=0.9)
                ax.grid(True, alpha=0.3)
        
        for i in range(len(cities), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        return fig
    
    def create_comparison_overview(self, df: pd.DataFrame, metric: str, 
                                  title: str, description: str):
        """Создание обзорного графика сравнения городов"""
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f"Обзор: {title}", fontsize=16, fontweight='bold')
        
        cities = sorted(df['city'].unique())
        
        # 1. Средние значения по городам
        city_avg = df.groupby('city')[metric].mean().sort_values(ascending=False)
        colors = [self.config.COLORS_PALETTE[i % len(self.config.COLORS_PALETTE)] 
                 for i in range(len(cities))]
        
        bars = ax1.bar(city_avg.index, city_avg.values * 100, color=colors, alpha=0.8)
        ax1.set_title('Средние значения по городам', fontweight='bold')
        ax1.set_ylabel('Конверсия (%)')
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0f}%'))
        
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 2. Распределение значений
        for i, city in enumerate(cities):
            city_data = df[df['city'] == city][metric] * 100
            color = self.config.COLORS_PALETTE[i % len(self.config.COLORS_PALETTE)]
            ax2.hist(city_data, alpha=0.6, label=city, color=color, bins=15)
        
        ax2.set_title('Распределение значений', fontweight='bold')
        ax2.set_xlabel('Конверсия (%)')
        ax2.set_ylabel('Частота')
        ax2.legend()
        
        # 3. Динамика по дням (средняя)
        daily_avg = df.groupby('day_order')[metric].mean()
        ax3.plot(daily_avg.index, daily_avg.values * 100, 
                color='#2E86AB', linewidth=3, marker='o', markersize=6)
        ax3.set_title('Общая динамика по дням', fontweight='bold')
        ax3.set_xlabel('День месяца')
        ax3.set_ylabel('Средняя конверсия (%)')
        ax3.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0f}%'))
        ax3.grid(True, alpha=0.3)
        
        # 4. Объем заказов по городам
        city_volume = df.groupby('city')['cnt_order'].sum().sort_values(ascending=False)
        bars = ax4.bar(city_volume.index, city_volume.values, color=colors, alpha=0.8)
        ax4.set_title('Объем заказов по городам', fontweight='bold')
        ax4.set_ylabel('Количество заказов')
        
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{height:,.0f}', ha='center', va='bottom', fontweight='bold')
        
        plt.setp(ax4.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        return fig
    
    def create_executive_dashboard(self, df: pd.DataFrame, alerts: Dict) -> plt.Figure:
        """Создание исполнительного дашборда"""
        
        fig = plt.figure(figsize=(20, 14))
        gs = GridSpec(4, 4, figure=fig, hspace=0.4, wspace=0.3)
        
        self._create_kpi_cards(fig, df, gs)
        self._create_trend_analysis(fig, df, gs)
        self._create_city_performance_grid(fig, df, gs)
        self._create_insights_panel(fig, df, alerts, gs)
        
        plt.suptitle('📊 Исполнительный дашборд - Аналитика такси', 
                    fontsize=20, fontweight='bold', y=0.98)
        
        return fig
    
    def _create_kpi_cards(self, fig, df, gs):
        """Создание KPI карточек"""
        kpis = [
            ("Общая конверсия", df['order2trip'].mean(), "%"),
            ("Всего заказов", df['cnt_order'].sum(), ""),
            ("Доля отложенных", df['delayed_ratio'].mean(), "%"),
            ("Среднее время задержки", df['avg_delay_minutes'].mean(), "мин")
        ]
        
        for i, (title, value, unit) in enumerate(kpis):
            ax = fig.add_subplot(gs[0, i])
            ax.axis('off')
            
            if "конверсия" in title.lower():
                color = '#28a745' if value > 0.4 else '#dc3545' if value < 0.3 else '#ffc107'
            elif "отложенных" in title.lower():
                color = '#28a745' if value < 0.3 else '#dc3545' if value > 0.6 else '#ffc107'
            else:
                color = '#17a2b8'
            
            if unit == "%":
                display_value = f"{value:.1%}"
            elif unit == "":
                display_value = f"{value:,.0f}"
            else:
                display_value = f"{value:.1f}{unit}"
            
            ax.text(0.5, 0.7, display_value, ha='center', va='center', 
                   fontsize=24, fontweight='bold', color=color, transform=ax.transAxes)
            ax.text(0.5, 0.3, title, ha='center', va='center', 
                   fontsize=12, transform=ax.transAxes)
            
            ax.add_patch(Rectangle((0.05, 0.1), 0.9, 0.8, 
                                 linewidth=2, edgecolor=color, facecolor='none', 
                                 transform=ax.transAxes))
    
    def _create_trend_analysis(self, fig, df, gs):
        """Трендовый анализ"""
        ax = fig.add_subplot(gs[1, :])
        
        trend_data = df.groupby('day_order').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum'
        })
        
        ax2 = ax.twinx()
        
        line1 = ax.plot(trend_data.index, trend_data['order2trip'] * 100, 
                       color='#2E86AB', linewidth=3, marker='o', markersize=6,
                       label='Базовая конверсия (%)')
        
        line2 = ax2.plot(trend_data.index, trend_data['delayed_ratio'] * 100, 
                        color='#A23B72', linewidth=3, marker='s', markersize=6,
                        label='Доля отложенных (%)')
        
        ax.set_xlabel('День месяца', fontweight='bold')
        ax.set_ylabel('Базовая конверсия (%)', color='#2E86AB', fontweight='bold')
        ax2.set_ylabel('Доля отложенных (%)', color='#A23B72', fontweight='bold')
        
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax.legend(lines, labels, loc='upper left')
        
        ax.set_title('Динамика ключевых метрик', fontweight='bold', pad=15)
        ax.grid(True, alpha=0.3)
    
    def _create_city_performance_grid(self, fig, df, gs):
        """Сетка производительности городов"""
        ax = fig.add_subplot(gs[2, :])
        
        cities = sorted(df['city'].unique())
        metrics = ['order2trip', 'delayed_ratio', 'cnt_order']
        metric_names = ['Конверсия', 'Доля отложенных', 'Объем заказов']
        
        city_stats = df.groupby('city').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum'
        })
        
        # Нормализация для heatmap
        city_stats_norm = city_stats.copy()
        city_stats_norm['order2trip'] = city_stats_norm['order2trip']
        city_stats_norm['delayed_ratio'] = 1 - city_stats_norm['delayed_ratio']  # Инвертируем для лучшей визуализации
        city_stats_norm['cnt_order'] = city_stats_norm['cnt_order'] / city_stats_norm['cnt_order'].max()
        
        # Создание heatmap
        data_for_heatmap = city_stats_norm[metrics].T
        data_for_heatmap.index = metric_names
        
        sns.heatmap(data_for_heatmap, annot=False, cmap='RdYlGn', 
                   cbar_kws={'label': 'Нормализованная производительность'}, ax=ax)
        
        # Добавление значений
        for i, city in enumerate(cities):
            for j, metric in enumerate(metrics):
                if metric == 'order2trip':
                    value = f"{city_stats.loc[city, metric]:.1%}"
                elif metric == 'delayed_ratio':
                    value = f"{city_stats.loc[city, metric]:.1%}"
                else:
                    value = f"{city_stats.loc[city, metric]:,.0f}"
                
                ax.text(i + 0.5, j + 0.5, value, ha='center', va='center',
                       fontweight='bold', fontsize=10)
        
        ax.set_title('Производительность по городам', fontweight='bold', pad=15)
        ax.set_xlabel('')
        ax.set_ylabel('')
    
    def _create_insights_panel(self, fig, df, alerts, gs):
        """Панель инсайтов"""
        ax = fig.add_subplot(gs[3, :])
        ax.axis('off')
        
        # Генерация автоматических инсайтов
        insights = self._generate_insights(df)
        
        insight_text = "💡 КЛЮЧЕВЫЕ ИНСАЙТЫ\n\n"
        for insight in insights[:5]:  # Показываем только топ-5
            insight_text += f"• {insight}\n"
        
        alert_text = "\n🚨 СИСТЕМА МОНИТОРИНГА\n\n"
        colors = {'critical': '🔴', 'warning': '🟡', 'info': '🔵'}
        
        for level, alert_list in alerts.items():
            if alert_list:
                alert_text += f"{colors[level]} {level.upper()}: {len(alert_list)} проблем\n"
        
        if not any(alerts.values()):
            alert_text += "✅ Критических проблем не обнаружено"
        
        full_text = insight_text + alert_text
        
        ax.text(0.05, 0.95, full_text, transform=ax.transAxes, 
               fontsize=11, verticalalignment='top',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='#f8f9fa', 
                        edgecolor='#dee2e6', alpha=0.9))
    
    def _generate_insights(self, df):
        """Генерация автоматических инсайтов"""
        insights = []
        
        # Лучший и худший город по конверсии
        city_performance = df.groupby('city')['order2trip'].mean().sort_values(ascending=False)
        best_city = city_performance.index[0]
        worst_city = city_performance.index[-1]
        insights.append(f"Лучшая конверсия в городе {best_city} ({city_performance[best_city]:.1%})")
        insights.append(f"Требует внимания: {worst_city} ({city_performance[worst_city]:.1%})")
        
        # Анализ отложенных заказов
        delayed_analysis = df.groupby('city')['delayed_ratio'].mean().sort_values(ascending=False)
        if delayed_analysis.iloc[0] > 0.5:
            insights.append(f"Высокий уровень отложенных заказов в городе {delayed_analysis.index[0]} ({delayed_analysis.iloc[0]:.1%})")
        
        # Объемы заказов
        volume_analysis = df.groupby('city')['cnt_order'].sum().sort_values(ascending=False)
        insights.append(f"Наибольший объем заказов: {volume_analysis.index[0]} ({volume_analysis.iloc[0]:,} заказов)")
        
        # Тренд по дням
        daily_trend = df.groupby('day_order')['order2trip'].mean()
        if daily_trend.iloc[-1] > daily_trend.iloc[0]:
            insights.append("Положительный тренд конверсии к концу периода")
        else:
            insights.append("Снижение конверсии к концу периода")
        
        return insights

# ============================================================================
# УПРОЩЕННАЯ СИСТЕМА АЛЕРТОВ
# ============================================================================
class SimpleAnomalyDetector:
    """Упрощенная система детекции аномалий"""
    
    @staticmethod
    def generate_alerts(df: pd.DataFrame, config: ReportConfig) -> Dict[str, List[Dict]]:
        """Генерация простых алертов"""
        alerts = {'critical': [], 'warning': [], 'info': []}
        
        low_conversion = df[df['order2trip'] < 0.2]
        for _, row in low_conversion.iterrows():
            alerts['critical'].append({
                'message': f"Низкая конверсия в {row['city']}: {row['order2trip']:.1%}"
            })
        
        high_delayed = df[df['delayed_ratio'] > 0.7]
        for _, row in high_delayed.iterrows():
            alerts['warning'].append({
                'message': f"Высокая доля отложенных в {row['city']}: {row['delayed_ratio']:.1%}"
            })
        
        return alerts

# ============================================================================
# ГЛАВНЫЙ КЛАСС
# ============================================================================
class TaxiReportGenerator:
    """Упрощенный главный класс"""
    
    def __init__(self, config: Optional[ReportConfig] = None):
        self.config = config or ReportConfig()
        self.visualizer = ModernVisualizer(self.config)
        self.anomaly_detector = SimpleAnomalyDetector()
        
    def load_and_process_data(self, filepath: Union[str, Path]) -> pd.DataFrame:
        """Загрузка и обработка данных"""
        df = pd.read_excel(filepath)
        df = DataProcessor.validate_dataframe(df)
        df = DataProcessor.parse_datetime_columns(df)
        df = DataProcessor.detect_delayed_orders(df, self.config.THRESHOLD_DELAYED_MINUTES)
        
        df['day_order'] = df['order_time'].dt.day
        df['hour_order'] = df['order_time'].dt.hour
        df['weekday'] = df['order_time'].dt.weekday
        
        return df
    
    def generate_beautiful_report(self, df: pd.DataFrame, 
                                cities: Optional[List[str]] = None,
                                save_plots: bool = False) -> Dict:
        """Генерация красивого отчета"""
        
        if cities is None:
            cities = sorted(df['city'].unique())
        
        df_filtered = df[df['city'].isin(cities)].copy()
        
        df_metrics = MetricsCalculator.calculate_conversion_metrics(
            df_filtered, ['day_order', 'city', 'is_delayed']
        )
        
        alerts = self.anomaly_detector.generate_alerts(df_metrics, self.config)
        
        figures = {}
        
        # Исполнительный дашборд
        figures['dashboard'] = self.visualizer.create_executive_dashboard(df_metrics, alerts)
        
        # Для каждой метрики создаем два графика: сравнительный обзор и отдельные графики по городам
        for metric, (title, ylim, description) in self.config.METRICS.items():
            if metric in df_metrics.columns:
                # Обзорный график сравнения
                figures[f'{metric}_overview'] = self.visualizer.create_comparison_overview(
                    df_metrics, metric, title, description
                )
                
                # Отдельные графики по городам
                figures[f'{metric}_cities'] = self.visualizer.create_city_separate_plots(
                    df_metrics, metric, title, description, ylim
                )
        
        if save_plots:
            for name, fig in figures.items():
                fig.savefig(f'enhanced_taxi_report_{name}.png', 
                           dpi=300, bbox_inches='tight', facecolor='white')
        
        for fig in figures.values():
            plt.show()
        
        return {
            'data': df_metrics,
            'alerts': alerts,
            'figures': figures,
            'summary_stats': self._generate_summary_stats(df_metrics)
        }
    
    def _generate_summary_stats(self, df: pd.DataFrame) -> Dict:
        """Генерация сводной статистики"""
        return {
            'total_orders': df['cnt_order'].sum(),
            'avg_conversion': df['order2trip'].mean(),
            'delayed_ratio': df['delayed_ratio'].mean(),
            'by_city': df.groupby('city').agg({
                'order2trip': 'mean',
                'delayed_ratio': 'mean',
                'cnt_order': 'sum'
            }).round(3).to_dict()
        }

# ============================================================================
# ОСНОВНАЯ ФУНКЦИЯ
# ============================================================================
def main():
    """Основная функция для запуска анализа"""
    
    config = ReportConfig()
    report_generator = TaxiReportGenerator(config)
    
    try:
        df = report_generator.load_and_process_data('taxi_data.xlsx')
        
        results = report_generator.generate_beautiful_report(
            df, 
            cities=None,
            save_plots=True
        )
        
        print("\n" + "="*60)
        print("📊 СВОДНАЯ СТАТИСТИКА")
        print("="*60)
        
        stats = results['summary_stats']
        
        print(f"Общее количество заказов: {stats['total_orders']:,}")
        print(f"Средняя конверсия: {stats['avg_conversion']:.1%}")
        print(f"Доля отложенных заказов: {stats['delayed_ratio']:.1%}")
        
        print("\n📈 СТАТИСТИКА ПО ГОРОДАМ:")
        print("-" * 60)
        
        for city, city_stats in stats['by_city'].items():
            print(f"\n🏙️ {city}:")
            print(f"  • Конверсия: {city_stats['order2trip']:.1%}")
            print(f"  • Доля отложенных: {city_stats['delayed_ratio']:.1%}")
            print(f"  • Количество заказов: {city_stats['cnt_order']:,}")
        
        print("\n🚨 АЛЕРТЫ:")
        print("-" * 60)
        
        alerts = results['alerts']
        for level, alert_list in alerts.items():
            if alert_list:
                print(f"\n{level.upper()}:")
                for alert in alert_list:
                    print(f"  • {alert['message']}")
        
        if not any(alerts.values()):
            print("✅ Критических проблем не обнаружено")
        
        print("\n" + "="*60)
        print("✅ Анализ завершен! Графики сохранены.")
        print("="*60)
        
    except FileNotFoundError:
        print("❌ Файл 'taxi_data.xlsx' не найден!")
        print("Убедитесь, что файл находится в текущей директории.")
        
    except Exception as e:
        print(f"❌ Произошла ошибка: {str(e)}")
        print("Проверьте формат данных и попробуйте еще раз.")

if __name__ == "__main__":
    main()